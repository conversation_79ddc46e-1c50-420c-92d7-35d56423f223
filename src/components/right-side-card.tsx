"use client";

import { GalleryV<PERSON>icalEnd, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>U<PERSON>, Book<PERSON><PERSON> } from "lucide-react";
import { motion } from "framer-motion";
import { TestimonialsCard } from "@/components/ui/testimonials-card";
import { testimonials } from "@/data/testimonials";

export function RightSideCard() {
  return (
    <div className="bg-muted relative hidden lg:flex lg:flex-col min-h-screen">
      {/* Branding Section - Top */}
      <div className="flex items-center justify-center p-8">
      
          {/* Logo and Brand */}
          <div className="space-y-4">
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="mx-auto w-16 h-16 flex items-center justify-center"
            >
            </motion.div>
            
            <div className="space-y-2">
              <motion.h1
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className="text-3xl font-bold text-foreground"
              >

              </motion.h1>
              <motion.p
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
                className="text-lg font-medium text-muted-foreground"
              >
              </motion.p>
            </div>
          </div>

          {/* Mission Description */}
          <motion.p
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
            className="text-muted-foreground leading-relaxed"
          >

          </motion.p>

          {/* Features Grid */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="grid grid-cols-2 gap-4"
          >

        </motion.div>
      </div>

      {/* Testimonials Section - Bottom */}
      <div className="flex items-center justify-center p-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="w-full"
        >
          <div className="text-center mb-6">
            <h2 className="text-2xl font-semibold text-foreground mb-2">
              Loved by Students
            </h2>
            <p className="text-sm text-muted-foreground">
              See what our users are saying about IsotopeAI
            </p>
          </div>
          <TestimonialsCard testimonials={testimonials} autoplay={true} />
        </motion.div>
      </div>

      {/* Decorative Elements */}
      <div className="absolute top-0 left-0 w-full h-full pointer-events-none overflow-hidden">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 0.1 }}
          transition={{ duration: 2 }}
          className="absolute -top-20 -right-20 w-40 h-40 rounded-full bg-gradient-to-br from-primary to-primary/50 blur-3xl"
        />
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 0.05 }}
          transition={{ duration: 2, delay: 0.5 }}
          className="absolute -bottom-20 -left-20 w-60 h-60 rounded-full bg-gradient-to-tr from-gradient-end to-gradient-start blur-3xl"
        />
      </div>
      {/* Copyright and Legal Links */}
      <div className="absolute bottom-4 left-0 w-full flex justify-between items-center px-8 text-xs text-muted-foreground">
        <span>Copyright &copy; 2025 IsotopeAI</span>
        <span>
          <a href="#" className="hover:underline">Privacy Policy</a> | <a href="#" className="hover:underline">Terms of Service</a>
        </span>
      </div>
    </div>
  );
}
