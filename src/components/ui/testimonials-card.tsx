"use client";

import { IconArrowLeft, IconArrowRight } from "@tabler/icons-react";
import { motion, AnimatePresence } from "framer-motion";
import { useEffect, useState } from "react";

type Testimonial = {
  quote: string;
  name: string;
  designation: string;
  src: string;
};

export const TestimonialsCard = ({
  testimonials,
  autoplay = true,
}: {
  testimonials: Testimonial[];
  autoplay?: boolean;
}) => {
  const [active, setActive] = useState(0);

  const handleNext = () => {
    setActive((prev) => (prev + 1) % testimonials.length);
  };

  const handlePrev = () => {
    setActive((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  const isActive = (index: number) => {
    return index === active;
  };

  useEffect(() => {
    if (autoplay) {
      const interval = setInterval(handleNext, 4000);
      return () => clearInterval(interval);
    }
  }, [autoplay]);

  const randomRotateY = () => {
    return Math.floor(Math.random() * 21) - 10;
  };

  return (
    <div className="w-full max-w-md mx-auto px-6 py-4">
      {/* Photo Section - Above */}
      <div className="relative h-32 w-32 mx-auto mb-4">
        <AnimatePresence>
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={testimonial.src}
              initial={{
                opacity: 0,
                scale: 0.8,
                z: -100,
                rotate: randomRotateY(),
              }}
              animate={{
                opacity: isActive(index) ? 1 : 0.3,
                scale: isActive(index) ? 1 : 0.85,
                z: isActive(index) ? 0 : -100,
                rotate: isActive(index) ? 0 : randomRotateY(),
                zIndex: isActive(index)
                  ? 40
                  : testimonials.length + 2 - index,
                y: isActive(index) ? [0, -20, 0] : 0,
              }}
              exit={{
                opacity: 0,
                scale: 0.8,
                z: 100,
                rotate: randomRotateY(),
              }}
              transition={{
                duration: 0.5,
                ease: "easeInOut",
              }}
              className="absolute inset-0 origin-bottom"
            >
              <img
                src={testimonial.src}
                alt={testimonial.name}
                width={192}
                height={192}
                draggable={false}
                className="h-full w-full rounded-full object-cover object-center shadow-xl ring-4 ring-white/20 dark:ring-white/10"
              />
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* Text Section - Below */}
      <div className="text-center space-y-6">
        <motion.div
          key={active}
          initial={{
            y: 20,
            opacity: 0,
          }}
          animate={{
            y: 0,
            opacity: 1,
          }}
          exit={{
            y: -20,
            opacity: 0,
          }}
          transition={{
            duration: 0.3,
            ease: "easeInOut",
          }}
          className="space-y-4"
        >
          <motion.p className="text-base leading-relaxed text-muted-foreground dark:text-neutral-300 italic">
            "{testimonials[active].quote.split(" ").map((word, index) => (
              <motion.span
                key={index}
                initial={{
                  filter: "blur(10px)",
                  opacity: 0,
                  y: 5,
                }}
                animate={{
                  filter: "blur(0px)",
                  opacity: 1,
                  y: 0,
                }}
                transition={{
                  duration: 0.2,
                  ease: "easeInOut",
                  delay: 0.02 * index,
                }}
                className="inline-block"
              >
                {word}&nbsp;
              </motion.span>
            ))}"
          </motion.p>
          
          <div className="space-y-1">
            <h3 className="text-xl font-semibold text-foreground">
              {testimonials[active].name}
            </h3>
            <p className="text-sm text-muted-foreground">
              {testimonials[active].designation}
            </p>
          </div>
        </motion.div>

        {/* Navigation Controls */}
        <div className="flex justify-center gap-3 pt-4">
          <button
            onClick={handlePrev}
            className="group/button flex h-8 w-8 items-center justify-center rounded-full bg-muted hover:bg-muted/80 transition-colors duration-200"
          >
            <IconArrowLeft className="h-4 w-4 text-muted-foreground transition-transform duration-300 group-hover/button:rotate-12" />
          </button>
          <button
            onClick={handleNext}
            className="group/button flex h-8 w-8 items-center justify-center rounded-full bg-muted hover:bg-muted/80 transition-colors duration-200"
          >
            <IconArrowRight className="h-4 w-4 text-muted-foreground transition-transform duration-300 group-hover/button:-rotate-12" />
          </button>
        </div>

        {/* Dots Indicator */}
        <div className="flex justify-center gap-2 pt-2">
          {testimonials.map((_, index) => (
            <button
              key={index}
              onClick={() => setActive(index)}
              className={`h-2 w-2 rounded-full transition-all duration-300 ${
                isActive(index)
                  ? "bg-primary w-6"
                  : "bg-muted-foreground/30 hover:bg-muted-foreground/50"
              }`}
            />
          ))}
        </div>
      </div>
    </div>
  );
};
