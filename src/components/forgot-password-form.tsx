"use client"

import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { ThemeToggleButton } from "@/components/theme/theme-toggle-button"

export function ForgotPasswordForm({
  className,
  ...props
}: React.ComponentProps<"form">) {
  return (
    <form className={cn("flex flex-col gap-6", className)} {...props}>
      <div className="absolute top-4 right-4 z-10">
        <ThemeToggleButton />
      </div>
      <div className="flex flex-col items-center gap-2 text-center">
        <h1 className="login-title text-foreground">Forgot your password?</h1>
        <p className="text-muted-foreground text-sm text-balance">
          Enter your email below to reset your password
        </p>
      </div>
      <div className="grid gap-6">
        <div className="grid gap-3">
          <Label htmlFor="email">Email</Label>
          <Input id="email" type="email" placeholder="<EMAIL>" required />
        </div>
        <Button type="submit" className="w-full">
          Reset Password
        </Button>
      </div>
      <div className="text-center text-sm">
        Remember your password?{" "}
        <a href="/" className="underline underline-offset-4 hover:text-[var(--primary)]">
          Login
        </a>
      </div>
    </form>
  )
}