"use client";

import { motion } from "framer-motion";
import { <PERSON>Circle, User, GraduationCap, Target, BookOpen, Sparkles } from "lucide-react";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import type { OnboardingData } from "@/app/onboarding/page";

interface StepThreeProps {
  data: OnboardingData;
  onComplete: () => void;
}

export function StepThreeSummary({ data, onComplete }: StepThreeProps) {
  return (
    <div className="space-y-8">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center space-y-2"
      >
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="mx-auto w-16 h-16 bg-gradient-to-r from-primary to-gradient-end rounded-full flex items-center justify-center mb-4"
        >
          <CheckCircle className="size-8 text-primary-foreground" />
        </motion.div>
        <h1 className="text-3xl font-bold text-foreground">
          Review & Finish
        </h1>
        <p className="text-muted-foreground text-lg">
          Almost there! Let's review your profile before we get started.
        </p>
      </motion.div>

      {/* Summary Card */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <Card className="overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-primary/5 to-gradient-end/5 border-b">
            <CardTitle className="flex items-center gap-2">
              <Sparkles className="size-5 text-primary" />
              Your Profile Summary
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6 space-y-6">
            {/* Avatar and Username */}
            <motion.div
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="flex items-center gap-4"
            >
              <div className="relative">
                {data.avatar && (
                  <div 
                    className="w-16 h-16 rounded-xl border-2 border-primary/20 overflow-hidden"
                    dangerouslySetInnerHTML={{ __html: data.avatar.svg }}
                  />
                )}
                <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-primary rounded-full flex items-center justify-center">
                  <User className="size-3 text-primary-foreground" />
                </div>
              </div>
              <div>
                <h3 className="text-xl font-semibold text-foreground">
                  {data.username}
                </h3>
                <p className="text-sm text-muted-foreground">
                  Your unique username
                </p>
              </div>
            </motion.div>

            <Separator />

            {/* Academic Information */}
            <div className="space-y-4">
              {/* Grade */}
              <motion.div
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className="flex items-center gap-3"
              >
                <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                  <GraduationCap className="size-5 text-primary" />
                </div>
                <div>
                  <p className="font-medium text-foreground">Grade/Level</p>
                  <p className="text-sm text-muted-foreground">{data.grade}</p>
                </div>
              </motion.div>

              {/* Exam (if selected) */}
              {data.exam && (
                <motion.div
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                  className="flex items-center gap-3"
                >
                  <div className="w-10 h-10 bg-gradient-to-r from-primary/10 to-gradient-end/10 rounded-lg flex items-center justify-center">
                    <Target className="size-5 text-primary" />
                  </div>
                  <div>
                    <p className="font-medium text-foreground">Target Exam</p>
                    <p className="text-sm text-muted-foreground">{data.exam}</p>
                  </div>
                </motion.div>
              )}

              {/* Subjects */}
              <motion.div
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.5 }}
                className="space-y-3"
              >
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-gradient-end/10 to-primary/10 rounded-lg flex items-center justify-center">
                    <BookOpen className="size-5 text-primary" />
                  </div>
                  <div>
                    <p className="font-medium text-foreground">
                      Subjects ({data.subjects.length})
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Your areas of focus
                    </p>
                  </div>
                </div>
                <div className="flex flex-wrap gap-2 ml-13">
                  {data.subjects.map((subject, index) => (
                    <motion.div
                      key={subject}
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.3, delay: 0.6 + index * 0.05 }}
                    >
                      <Badge variant="secondary" className="px-3 py-1">
                        {subject}
                      </Badge>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Completion Message */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
        className="text-center space-y-4"
      >
        <Card className="p-6 bg-gradient-to-r from-primary/5 to-gradient-end/5 border-primary/20">
          <CardContent className="space-y-4">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="mx-auto w-12 h-12 bg-gradient-to-r from-primary to-gradient-end rounded-full flex items-center justify-center"
            >
              <Sparkles className="size-6 text-primary-foreground" />
            </motion.div>
            <div>
              <h3 className="text-xl font-semibold text-foreground mb-2">
                You're all set!
              </h3>
              <p className="text-muted-foreground">
                Let's get started with your personalized dashboard 🚀
              </p>
            </div>
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.5 }}
            >
              <Button
                onClick={onComplete}
                size="lg"
                className="bg-gradient-to-r from-primary to-gradient-end hover:from-primary/90 hover:to-gradient-end/90 text-lg px-8 py-3"
              >
                Complete Profile
              </Button>
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Motivational Footer */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.6 }}
        className="text-center"
      >
        <p className="text-sm text-muted-foreground">
          Ready to unlock your potential? Your learning journey begins now! ✨
        </p>
      </motion.div>
    </div>
  );
}
