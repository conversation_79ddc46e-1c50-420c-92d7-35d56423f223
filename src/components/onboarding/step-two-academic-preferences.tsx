"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { GraduationCap, Target, BookOpen, X } from "lucide-react";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import type { OnboardingData } from "@/app/onboarding/page";

interface StepTwoProps {
  data: OnboardingData;
  updateData: (updates: Partial<OnboardingData>) => void;
}

const grades = [
  "Class 6", "Class 7", "Class 8", "Class 9", "Class 10",
  "Class 11", "Class 12", "Undergraduate", "Postgraduate", 
  "Gap Year", "Other"
];

const exams = [
  { id: "jee", name: "<PERSON><PERSON> (Joint Entrance Examination)", subjects: ["Physics", "Chemistry", "Mathematics"] },
  { id: "neet", name: "NEET (Medical Entrance)", subjects: ["Physics", "Chemistry", "Biology"] },
  { id: "gate", name: "GATE (Graduate Aptitude Test)", subjects: ["Mathematics", "Core Subject"] },
  { id: "sat", name: "SAT (Scholastic Assessment Test)", subjects: ["Mathematics", "English"] },
  { id: "gre", name: "GRE (Graduate Record Examination)", subjects: ["Quantitative Reasoning", "Verbal Reasoning", "Analytical Writing"] },
  { id: "gmat", name: "GMAT (Graduate Management Admission Test)", subjects: ["Quantitative", "Verbal", "Analytical Writing", "Integrated Reasoning"] },
  { id: "cuet", name: "CUET (Common University Entrance Test)", subjects: ["General Test", "Domain Subjects"] },
  { id: "ielts", name: "IELTS (International English Language Testing)", subjects: ["Listening", "Reading", "Writing", "Speaking"] },
  { id: "toefl", name: "TOEFL (Test of English as a Foreign Language)", subjects: ["Reading", "Listening", "Speaking", "Writing"] },
];

const allSubjects = [
  "Physics", "Chemistry", "Mathematics", "Biology", "English", 
  "Computer Science", "Economics", "History", "Geography", "Political Science",
  "Psychology", "Sociology", "Philosophy", "Literature", "Statistics",
  "Quantitative Reasoning", "Verbal Reasoning", "Logical Reasoning",
  "Analytical Writing", "General Knowledge", "Current Affairs"
];

export function StepTwoAcademicPreferences({ data, updateData }: StepTwoProps) {
  const [mode, setMode] = useState<"exam" | "subjects">("exam");

  const handleGradeChange = (grade: string) => {
    updateData({ grade });
  };

  const handleExamChange = (examId: string) => {
    const exam = exams.find(e => e.id === examId);
    if (exam) {
      updateData({ 
        exam: exam.name,
        subjects: exam.subjects 
      });
    }
  };

  const handleSubjectToggle = (subject: string) => {
    const currentSubjects = data.subjects;
    const newSubjects = currentSubjects.includes(subject)
      ? currentSubjects.filter(s => s !== subject)
      : [...currentSubjects, subject];
    
    updateData({ subjects: newSubjects });
  };

  const handleModeChange = (newMode: "exam" | "subjects") => {
    setMode(newMode);
    if (newMode === "subjects") {
      updateData({ exam: null });
    } else {
      updateData({ subjects: [] });
    }
  };

  const removeSubject = (subject: string) => {
    updateData({ 
      subjects: data.subjects.filter(s => s !== subject) 
    });
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center space-y-2"
      >
        <h1 className="text-3xl font-bold text-foreground">
          What are you studying?
        </h1>
        <p className="text-muted-foreground text-lg">
          Your journey starts here! Tell us about your academic goals.
        </p>
      </motion.div>

      {/* Grade Selection */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <Card className="p-6">
          <CardContent className="space-y-4">
            <div className="flex items-center gap-2">
              <GraduationCap className="size-5 text-primary" />
              <Label className="text-lg font-semibold">
                Select your grade/level
              </Label>
            </div>
            <Select value={data.grade || ""} onValueChange={handleGradeChange}>
              <SelectTrigger className="text-lg">
                <SelectValue placeholder="Choose your current grade or level" />
              </SelectTrigger>
              <SelectContent>
                {grades.map((grade) => (
                  <SelectItem key={grade} value={grade}>
                    {grade}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </CardContent>
        </Card>
      </motion.div>

      {/* Exam or Subjects Toggle */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <Card className="p-6">
          <CardContent className="space-y-6">
            <div className="flex items-center gap-2">
              <Target className="size-5 text-primary" />
              <Label className="text-lg font-semibold">
                Choose your focus
              </Label>
            </div>

            {/* Mode Toggle */}
            <div className="flex items-center gap-2 p-1 bg-muted rounded-lg">
              <Button
                variant={mode === "exam" ? "default" : "ghost"}
                size="sm"
                onClick={() => handleModeChange("exam")}
                className="flex-1"
              >
                Preparing for an exam
              </Button>
              <Button
                variant={mode === "subjects" ? "default" : "ghost"}
                size="sm"
                onClick={() => handleModeChange("subjects")}
                className="flex-1"
              >
                Studying subjects
              </Button>
            </div>

            <Separator />

            {/* Exam Selection */}
            {mode === "exam" && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-4"
              >
                <Label className="text-base font-medium">
                  Which exam are you preparing for?
                </Label>
                <Select value={data.exam || ""} onValueChange={handleExamChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select an exam" />
                  </SelectTrigger>
                  <SelectContent>
                    {exams.map((exam) => (
                      <SelectItem key={exam.id} value={exam.id}>
                        {exam.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {data.exam && data.subjects.length > 0 && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="space-y-3"
                  >
                    <Label className="text-sm text-muted-foreground">
                      Subjects for this exam (you can modify these):
                    </Label>
                    <div className="flex flex-wrap gap-2">
                      {data.subjects.map((subject) => (
                        <Badge
                          key={subject}
                          variant="secondary"
                          className="flex items-center gap-1 px-3 py-1"
                        >
                          {subject}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeSubject(subject)}
                            className="h-auto p-0 hover:bg-transparent"
                          >
                            <X className="size-3" />
                          </Button>
                        </Badge>
                      ))}
                    </div>
                  </motion.div>
                )}
              </motion.div>
            )}

            {/* Subject Selection */}
            {mode === "subjects" && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-4"
              >
                <Label className="text-base font-medium">
                  Select the subjects you're studying:
                </Label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {allSubjects.map((subject) => (
                    <Button
                      key={subject}
                      variant={data.subjects.includes(subject) ? "default" : "outline"}
                      size="sm"
                      onClick={() => handleSubjectToggle(subject)}
                      className="justify-start text-left h-auto py-2"
                    >
                      {subject}
                    </Button>
                  ))}
                </div>
              </motion.div>
            )}

            {/* Selected Subjects Display */}
            {data.subjects.length > 0 && mode === "subjects" && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-3"
              >
                <Label className="text-sm text-muted-foreground">
                  Selected subjects ({data.subjects.length}):
                </Label>
                <div className="flex flex-wrap gap-2">
                  {data.subjects.map((subject) => (
                    <Badge
                      key={subject}
                      variant="default"
                      className="flex items-center gap-1 px-3 py-1"
                    >
                      {subject}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeSubject(subject)}
                        className="h-auto p-0 hover:bg-transparent text-primary-foreground/70 hover:text-primary-foreground"
                      >
                        <X className="size-3" />
                      </Button>
                    </Badge>
                  ))}
                </div>
              </motion.div>
            )}
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
