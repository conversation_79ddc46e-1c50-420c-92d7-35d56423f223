"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { GalleryVerticalEnd, ArrowLeft, ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { ThemeToggleButton } from "@/components/theme/theme-toggle-button";
import { StepOneAvatarUsername } from "@/components/onboarding/step-one-avatar-username";
import { StepTwoAcademicPreferences } from "@/components/onboarding/step-two-academic-preferences";
import { StepThreeSummary } from "@/components/onboarding/step-three-summary";

export interface OnboardingData {
  username: string;
  avatar: {
    type: "multiavatar";
    seed: string;
    svg: string;
  } | null;
  grade: string | null;
  exam: string | null;
  subjects: string[];
  step: 1 | 2 | 3;
}

export default function OnboardingPage() {
  const [data, setData] = useState<OnboardingData>({
    username: "",
    avatar: null,
    grade: null,
    exam: null,
    subjects: [],
    step: 1,
  });

  const updateData = (updates: Partial<OnboardingData>) => {
    setData(prev => ({ ...prev, ...updates }));
  };

  const nextStep = () => {
    if (data.step < 3) {
      setData(prev => ({ ...prev, step: (prev.step + 1) as 1 | 2 | 3 }));
    }
  };

  const prevStep = () => {
    if (data.step > 1) {
      setData(prev => ({ ...prev, step: (prev.step - 1) as 1 | 2 | 3 }));
    }
  };

  const canProceedFromStep1 = data.username.length >= 3 && data.username.length <= 20 && 
    /^[a-zA-Z0-9_]+$/.test(data.username) && data.avatar !== null;

  const canProceedFromStep2 = data.grade !== null && data.subjects.length > 0;

  const handleComplete = () => {
    // Placeholder for backend integration
    console.log("Profile completed:", data);
    // TODO: Navigate to dashboard
    alert("Profile completed! Redirecting to dashboard...");
  };

  const progressValue = (data.step / 3) * 100;

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="relative border-b border-border/50">
        <div className="flex items-center justify-between p-6">
          {/* Brand */}
          <div className="flex items-center gap-2">
            <div className="bg-primary text-primary-foreground flex size-8 items-center justify-center rounded-lg">
              <GalleryVerticalEnd className="size-4" />
            </div>
            <span className="text-xl font-bold text-foreground">IsotopeAI</span>
          </div>

          {/* Theme Toggle */}
          <ThemeToggleButton />
        </div>

        {/* Progress Bar */}
        <div className="px-6 pb-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-muted-foreground">
              Step {data.step} of 3
            </span>
            <span className="text-sm font-medium text-muted-foreground">
              {Math.round(progressValue)}% Complete
            </span>
          </div>
          <Progress value={progressValue} className="h-2" />
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex items-center justify-center p-6">
        <div className="w-full max-w-2xl">
          <AnimatePresence mode="wait">
            {data.step === 1 && (
              <motion.div
                key="step1"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                <StepOneAvatarUsername 
                  data={data} 
                  updateData={updateData} 
                />
              </motion.div>
            )}

            {data.step === 2 && (
              <motion.div
                key="step2"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                <StepTwoAcademicPreferences 
                  data={data} 
                  updateData={updateData} 
                />
              </motion.div>
            )}

            {data.step === 3 && (
              <motion.div
                key="step3"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                <StepThreeSummary 
                  data={data} 
                  onComplete={handleComplete}
                />
              </motion.div>
            )}
          </AnimatePresence>

          {/* Navigation */}
          <motion.div 
            className="flex items-center justify-between mt-8"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Button
              variant="outline"
              onClick={prevStep}
              disabled={data.step === 1}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="size-4" />
              Back
            </Button>

            {data.step < 3 ? (
              <Button
                onClick={nextStep}
                disabled={
                  (data.step === 1 && !canProceedFromStep1) ||
                  (data.step === 2 && !canProceedFromStep2)
                }
                className="flex items-center gap-2"
              >
                Next
                <ArrowRight className="size-4" />
              </Button>
            ) : (
              <Button
                onClick={handleComplete}
                className="flex items-center gap-2 bg-gradient-to-r from-primary to-gradient-end hover:from-primary/90 hover:to-gradient-end/90"
              >
                Complete Profile
                <ArrowRight className="size-4" />
              </Button>
            )}
          </motion.div>
        </div>
      </div>

      {/* Decorative Elements */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden -z-10">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 0.03 }}
          transition={{ duration: 2 }}
          className="absolute -top-40 -right-40 w-80 h-80 rounded-full bg-gradient-to-br from-primary to-gradient-end blur-3xl"
        />
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 0.02 }}
          transition={{ duration: 2, delay: 0.5 }}
          className="absolute -bottom-40 -left-40 w-96 h-96 rounded-full bg-gradient-to-tr from-gradient-end to-primary blur-3xl"
        />
      </div>
    </div>
  );
}
